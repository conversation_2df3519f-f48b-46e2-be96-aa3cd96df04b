using LibBaseModules.DB;
using SqlSugar;
using System;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 故障信息
    /// </summary>
    [SugarTable("fault_info")]
    [SugarIndex("index_sncode_time", nameof(SNCode), OrderByType.Asc, nameof(RecordTime), OrderByType.Desc)]
    [Description("故障信息")]
    public class FaultInfo : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 主键ID（自增长）
        /// </summary>
        [SugarColumn(ColumnName = "id", ColumnDescription = "主键ID", IsPrimaryKey = true, IsIdentity = true)]
        [Description("主键ID")]
        public int Id { get; set; }

        /// <summary>
        /// 设备序列号（必填）
        /// </summary>
        [SugarColumn(ColumnName = "sncode", ColumnDescription = "设备序列号")]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 录入时间（自动生成当前时间）
        /// </summary>
        [SugarColumn(ColumnName = "recordtime", SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "录入时间")]
        [Description("录入时间")]
        public DateTime RecordTime { get; set; }

        /// <summary>
        /// 因子（必填）
        /// </summary>
        [SugarColumn(ColumnName = "factor", ColumnDescription = "因子", Length = 50)]
        [Description("因子")]
        public string Factor { get; set; }

        /// <summary>
        /// 类别1（必填）
        /// </summary>
        [SugarColumn(ColumnName = "category1", ColumnDescription = "类别1", Length = 50)]
        [Description("类别1")]
        public string Category1 { get; set; }

        /// <summary>
        /// 类别2（必填）
        /// </summary>
        [SugarColumn(ColumnName = "category2", ColumnDescription = "类别2", Length = 50)]
        [Description("类别2")]
        public string Category2 { get; set; }

        /// <summary>
        /// 问题描述（必填，支持多行文本）
        /// </summary>
        [SugarColumn(ColumnName = "problem_description", ColumnDescription = "问题描述", Length = 1000)]
        [Description("问题描述")]
        public string ProblemDescription { get; set; }

        #endregion

        #region 构造函数

        public FaultInfo()
        {
            RecordTime = DateTime.Now;
        }

        #endregion
    }
}