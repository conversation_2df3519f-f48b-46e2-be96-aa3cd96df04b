﻿using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.Config.FaultManagement.Models
{
    /// <summary>
    /// 完整的故障数据结构
    /// 完整的故障数据结构，主要包含一级故障分类列表
    /// 此类作为故障配置的根节点
    /// </summary>
    public class FaultInfo
    {
        /// <summary>
        /// 一级故障分类列表
        /// </summary>
        [Description("一级故障分类列表")]
        public List<FaultCategory1> Category1List { get; set; } = new List<FaultCategory1>();
    }

    /// <summary>
    /// 故障一级分类模型
    /// </summary>
    public class FaultCategory1
    {
        /// <summary>
        /// 故障ID
        /// </summary>
        [Description("故障ID")]
        public int FaultId { get; set; }

        /// <summary>
        /// 故障名称
        /// </summary>
        [Description("故障名称")]
        public string FaultName { get; set; }

        /// <summary>
        /// 故障描述信息
        /// </summary>
        [Description("故障描述信息")]
        public string Description { get; set; }

        /// <summary>
        /// 包含的二级分类
        /// </summary>
        [Description("包含的二级分类")]
        public List<FaultCategory2> SubCategories { get; set; } = new List<FaultCategory2>();
    }

    /// <summary>
    /// 故障二级分类模型
    /// </summary>
    public class FaultCategory2
    {
        /// <summary>
        /// 父故障ID
        /// </summary>
        [Description("父故障ID")]
        public int ParentFaultId { get; set; }

        /// <summary>
        /// 故障ID
        /// </summary>
        [Description("故障ID")]
        public int FaultId { get; set; }

        /// <summary>
        /// 故障名称
        /// </summary>
        [Description("故障名称")]
        public string FaultName { get; set; }

        /// <summary>
        /// 故障描述信息
        /// </summary>
        [Description("故障描述信息")]
        public string Description { get; set; }
    }

    /// <summary>
    /// 故障记录模型
    /// </summary>
    public class FaultRecord
    {
        /// <summary>
        /// 故障记录的唯一标识ID
        /// </summary>
        [Description("故障记录的唯一标识ID")]
        public int Id { get; set; }

        /// <summary>
        /// 故障所属的一级分类ID
        /// </summary>
        [Description("故障所属的一级分类ID")]
        public int Category1Id { get; set; }

        /// <summary>
        /// 故障所属的二级分类ID
        /// </summary>
        [Description("故障所属的二级分类ID")]
        public int Category2Id { get; set; }

        /// <summary>
        /// 故障的标题
        /// </summary>
        [Description("故障的标题")]
        public string FaultTitle { get; set; }

        /// <summary>
        /// 故障的详细描述
        /// </summary>
        [Description("故障的详细描述")]
        public string FaultDescription { get; set; }

        /// <summary>
        /// 故障的严重程度，默认为 Low
        /// </summary>
        [Description("故障的严重程度")]
        public FaultSeverity Severity { get; set; } = FaultSeverity.Low;

        /// <summary>
        /// 故障的当前状态，默认为 Pending
        /// </summary>
        [Description("故障的当前状态")]
        public FaultStatus Status { get; set; } = FaultStatus.Pending;

        /// <summary>
        /// 报告者的ID
        /// </summary>
        [Description("报告者的ID")]
        public string ReporterId { get; set; }

        /// <summary>
        /// 报告者的名称
        /// </summary>
        [Description("报告者的名称")]
        public string ReporterName { get; set; }

        /// <summary>
        /// 故障的创建时间，默认为当前时间
        /// </summary>
        [Description("故障的创建时间")]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 故障的最后更新时间，默认为当前时间
        /// </summary>
        [Description("故障的最后更新时间")]
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 故障的解决时间，如果尚未解决则为 null
        /// </summary>
        [Description("故障的解决时间")]
        public DateTime? ResolveTime { get; set; }
    }

    /// <summary>
    /// 故障严重程度枚举
    /// </summary>
    public enum FaultSeverity
    {
        /// <summary>
        /// 低严重程度
        /// </summary>
        [Description("低严重程度")]
        Low = 1,

        /// <summary>
        /// 中等严重程度
        /// </summary>
        [Description("中等严重程度")]
        Medium = 2,

        /// <summary>
        /// 高严重程度
        /// </summary>
        [Description("高严重程度")]
        High = 3,

        /// <summary>
        /// 紧急严重程度
        /// </summary>
        [Description("紧急严重程度")]
        Critical = 4
    }

    /// <summary>
    /// 故障状态枚举
    /// 表示故障处理的当前状态
    /// </summary>
    public enum FaultStatus
    {
        /// <summary>
        /// 故障待处理
        /// </summary>
        [Description("故障待处理")]
        Pending = 1,

        /// <summary>
        /// 故障处理中
        /// </summary>
        [Description("故障处理中")]
        InProgress = 2,

        /// <summary>
        /// 故障已解决
        /// </summary>
        [Description("故障已解决")]
        Resolved = 3,

        /// <summary>
        /// 故障已关闭
        /// </summary>
        [Description("故障已关闭")]
        Closed = 4
    }
}