using Fpi.Util;
using LibBaseModules.Helper;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using SqlSugar;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;

namespace LibBusinessModules.UI.Fault
{
    /// <summary>
    /// 故障管理界面
    /// </summary>
    public partial class UC_FaultManager : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 当前页数
        /// </summary>
        private int CurPage = 1;

        /// <summary>
        /// 总共页数
        /// </summary>
        private int PageCount;

        /// <summary>
        /// 每页记录数
        /// </summary>
        private int PageSize;

        /// <summary>
        /// SN码
        /// </summary>
        private string SnCode;

        /// <summary>
        /// 查询起始时间
        /// </summary>
        private DateTime StartTime;

        /// <summary>
        /// 查询结束时间
        /// </summary>
        private DateTime EndTime;

        /// <summary>
        /// 本次查到的总数据量
        /// </summary>
        private int RecordCount;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        #endregion

        #region 构造函数

        public UC_FaultManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件处理

        private void UC_FaultManager_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                InitialDateTimePicker();
                SetViewHead();

                // 禁用点击列表头排序
                foreach(DataGridViewColumn column in dgvRecords.Columns)
                {
                    column.SortMode = DataGridViewColumnSortMode.NotSortable;
                }
            }
        }

        /// <summary>
        /// 新增故障按钮点击事件
        /// </summary>
        private void btnAddFault_Click(object sender, EventArgs e)
        {
            try
            {
                using(var frmAdd = new FrmFaultAdd())
                {
                    if(frmAdd.ShowDialog() == DialogResult.OK)
                    {
                        // 刷新数据
                        if(RecordCount > 0)
                        {
                            btnQuery_Click(sender, e);
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"打开新增故障窗口失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private void btnQuery_Click(object sender, EventArgs e)
        {
            dgvRecords.Rows.Clear();
            CurPage = 1;
            lblPage.Text = @"?/?";

            StartTime = dtpStartTime.Value;
            EndTime = dtpEndTime.Value;

            try
            {
                if(!uint.TryParse(txtRecordCount.Text, out uint _))
                {
                    throw new FormatException("每页记录数应为整数，请重新输入！");
                }

                if(StartTime >= EndTime)
                {
                    throw new Exception("起始时间不能大于结束时间！");
                }

                SnCode = txtSnCode.Text;
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError(ex.Message);
                return;
            }

            Enabled = false;

            try
            {
                PageSize = int.Parse(txtRecordCount.Text);

                // 查询数据数量
                try
                {
                    RecordCount = QueryDataCount();
                }
                catch(Exception ex)
                {
                    throw new Exception($"数据库连接异常:{ex.Message}");
                }

                if(RecordCount > 0)
                {
                    PageCount = (RecordCount - 1) / PageSize + 1;
                    QueryResult();
                }
                else
                {
                    PageCount = 0;
                    throw new Exception($"没有此时间段的故障记录！");
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }

            Enabled = true;
            dgvRecords.Focus();
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;
                if(dgvRecords.Rows.Count <= 0)
                {
                    throw new Exception("无数据可导出！");
                }
                else
                {
                    // 导出时弹窗提示选取导出目录，及文件名称
                    string filePath = Application.StartupPath + "\\export\\";
                    if(!Directory.Exists(filePath))
                    {
                        Directory.CreateDirectory(filePath);
                    }

                    saveFileDialog.InitialDirectory = filePath;
                    saveFileDialog.FileName = $"{filePath}故障记录{StartTime:yyyy-MM-dd HH：mm：ss}--{EndTime:yyyy-MM-dd HH：mm：ss}.xlsx";

                    if(saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        // 显示等待界面
                        UIFormServiceHelper.ShowWaitForm(ParentForm, "数据导出中，请稍候...");

                        // 导出
                        FileExportHelper.SaveDataGridViewToExcelFile(ParentForm, dgvRecords, saveFileDialog.FileName);

                        // 隐藏等待界面
                        UIFormServiceHelper.HideWaitForm(ParentForm);

                        if(UIMessageBox.ShowAsk("导出成功！是否定位到文件所在位置？"))
                        {
                            var psi = new ProcessStartInfo("Explorer.exe")
                            {
                                Arguments = "/e,/select," + saveFileDialog.FileName
                            };
                            // 打开导出文件所在位置
                            Process.Start(psi);
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
                UIMessageBox.ShowError(ex.Message);
            }
            finally
            {
                Enabled = true;
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
            }
        }

        /// <summary>
        /// 删除选中记录按钮点击事件
        /// </summary>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedRows = dgvRecords.SelectedRows;
                if(selectedRows.Count == 0)
                {
                    UIMessageBox.ShowWarning("请选择要删除的记录！");
                    return;
                }

                if(!UIMessageBox.ShowAsk($"确定要删除选中的 {selectedRows.Count} 条记录吗？"))
                {
                    return;
                }

                var infosToDelete = new List<FaultInfo>();
                foreach(DataGridViewRow row in selectedRows)
                {
                    if(row.Tag is FaultInfo faultInfo)
                    {
                        infosToDelete.Add(faultInfo);
                    }
                }

                if(infosToDelete.Count > 0)
                {
                    var db = DBHelper.GetPCDBContext();
                    db.Deleteable(infosToDelete).ExecuteCommand();

                    UIMessageBox.ShowSuccess($"成功删除 {infosToDelete.Count} 条记录！");

                    // 刷新数据
                    btnQuery_Click(sender, e);
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"删除记录失败：{ex.Message}");
            }
        }

        #region 翻页事件

        private void btnFirst_Click(object sender, EventArgs e)
        {
            CurPage = 1;
            try
            {
                QueryResult();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            if(CurPage > 1)
            {
                CurPage--;
                try
                {
                    QueryResult();
                }
                catch(Exception ex)
                {
                    UIMessageBox.ShowError("查询数据错误:" + ex.Message);
                }
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if(CurPage < PageCount)
            {
                CurPage++;
                try
                {
                    QueryResult();
                }
                catch(Exception ex)
                {
                    UIMessageBox.ShowError("查询数据错误:" + ex.Message);
                }
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            if(PageCount <= 0)
                return;
            CurPage = PageCount;
            try
            {
                QueryResult();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }
        }

        #endregion

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            dtpStartTime.Value = DateTime.Today;
            dtpEndTime.Value = DateTime.Today + new TimeSpan(23, 59, 59);
        }

        /// <summary>
        /// 设置表头
        /// </summary>
        private void SetViewHead()
        {
            dgvRecords.Columns.Clear();
            dgvRecords.Columns.Add("Num", "序号");
            dgvRecords.Columns.Add("RecordTime", "录入时间");
            dgvRecords.Columns.Add("InstrumentId", "仪表ID");
            dgvRecords.Columns.Add("Factor", "因子");
            dgvRecords.Columns.Add("Category1", "类别1");
            dgvRecords.Columns.Add("Category2", "类别2");
            dgvRecords.Columns.Add("ProblemDescription", "问题描述");

            //// 设置列宽
            //dgvRecords.Columns["Num"].Width = 60;
            //dgvRecords.Columns["RecordTime"].Width = 150;
            //dgvRecords.Columns["InstrumentId"].Width = 150;
            //dgvRecords.Columns["Factor"].Width = 80;
            //dgvRecords.Columns["Category1"].Width = 100;
            //dgvRecords.Columns["Category2"].Width = 100;
            //dgvRecords.Columns["ProblemDescription"].Width = 300;
        }

        /// <summary>
        /// 查询数据总数
        /// </summary>
        /// <returns></returns>
        private int QueryDataCount()
        {
            var query = DBHelper.GetPCDBContext().Queryable<FaultInfo>()
                           .Where(data => data.RecordTime >= StartTime && data.RecordTime <= EndTime);

            // 添加查询条件
            if(!string.IsNullOrEmpty(SnCode))
            {
                query = query.Where(data => data.SNCode.Contains(SnCode));
            }

            return query.Count();
        }

        /// <summary>
        /// 得到查询结果并更新界面
        /// </summary>
        private void QueryResult()
        {
            if(PageCount <= 0)
            {
                return;
            }

            lblPage.Text = $"{CurPage}/{PageCount}";

            FillDataToDgv();

            ManageEnable();
        }

        /// <summary>
        /// 填充数据到DataGridView
        /// </summary>
        private void FillDataToDgv()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");

                // 查询当前页数据
                var query = DBHelper.GetPCDBContext().Queryable<FaultInfo>()
                               .Where(data => data.RecordTime >= StartTime && data.RecordTime <= EndTime);

                // 添加查询条件
                if(!string.IsNullOrEmpty(SnCode))
                {
                    query = query.Where(data => data.SNCode.Contains(SnCode));
                }

                // 按时间倒序排列
                var dataList = query.OrderBy(data => data.RecordTime, OrderByType.Desc)
                                   .ToPageList(CurPage - 1, PageSize);

                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                dgvRecords.Rows.Clear();
                UIFormServiceHelper.ShowStatusForm(this.ParentForm, dataList.Count, "数据渲染中，请稍候...");

                int index = (CurPage - 1) * PageSize + 1;
                foreach(var data in dataList)
                {
                    int rowIndex = dgvRecords.AddRow();
                    DataGridViewRow dr = dgvRecords.Rows[rowIndex];
                    dr.Cells["Num"].Value = index;
                    dr.Cells["RecordTime"].Value = data.RecordTime.ToDisplayFormat();
                    dr.Cells["InstrumentId"].Value = data.SNCode;
                    dr.Cells["Factor"].Value = data.Factor;
                    dr.Cells["Category1"].Value = data.Category1;
                    dr.Cells["Category2"].Value = data.Category2;
                    dr.Cells["ProblemDescription"].Value = data.ProblemDescription;

                    dr.Tag = data;

                    UIFormServiceHelper.SetStatusFormDescription(this.ParentForm, $"数据渲染中[{index}/{RecordCount}]......");
                    UIFormServiceHelper.SetStatusFormStepIt(this.ParentForm, index);
                    index++;
                }
            }
            finally
            {
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
                UIFormServiceHelper.HideStatusForm(this.ParentForm);
            }
        }

        /// <summary>
        /// 控制页数按钮的Enable属性
        /// </summary>
        private void ManageEnable()
        {
            if(CurPage == 1)
            {
                btnFirst.Enabled = btnPrev.Enabled = false;
            }
            else
            {
                btnFirst.Enabled = btnPrev.Enabled = true;
            }

            if(CurPage == PageCount)
            {
                btnEnd.Enabled = btnNext.Enabled = false;
            }
            else
            {
                btnEnd.Enabled = btnNext.Enabled = true;
            }
        }

        #endregion
    }
}
