using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using Sunny.UI;
using System;
using System.Windows.Forms;

namespace LibBusinessModules.UI.Fault
{
    /// <summary>
    /// 故障添加对话框
    /// </summary>
    public partial class FrmFaultAdd : UIForm
    {
        #region 字段属性

        /// <summary>
        /// 添加的故障信息
        /// </summary>
        public FaultInfo FaultInfo { get; private set; }

        #endregion

        #region 构造函数

        public FrmFaultAdd()
        {
            InitializeComponent();
            InitializeForm();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            // 设置窗体属性
            this.Text = "新增故障";
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            // 初始化时间
            dtpRecordTime.Value = DateTime.Now;
            dtpRecordTime.Enabled = false; // 时间自动录入，不允许修改

            // 设置因子下拉选项
            cmbFactor.Items.AddRange(new string[] { "COD", "NH4", "TP", "TN", "IMN" });

            // 设置类别1下拉选项
            cmbCategory1.Items.AddRange(new string[] { "硬件", "软件", "流路", "光学", "电气", "机械" });

            // 设置类别2下拉选项
            cmbCategory2.Items.AddRange(new string[] { "柱塞泵", "选向阀", "光源", "检测器", "主板", "屏幕", "管路", "其他" });

            // 设置多行文本框
            txtProblemDescription.Multiline = true;
        }

        /// <summary>
        /// 验证输入数据
        /// </summary>
        /// <returns></returns>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtSNCode.Text))
            {
                UIMessageBox.ShowError("请输入设备序列号！");
                txtSNCode.Focus();
                return false;
            }

            if (cmbFactor.SelectedIndex == -1)
            {
                UIMessageBox.ShowError("请选择因子！");
                cmbFactor.Focus();
                return false;
            }

            if (cmbCategory1.SelectedIndex == -1)
            {
                UIMessageBox.ShowError("请选择类别1！");
                cmbCategory1.Focus();
                return false;
            }

            if (cmbCategory2.SelectedIndex == -1)
            {
                UIMessageBox.ShowError("请选择类别2！");
                cmbCategory2.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtProblemDescription.Text))
            {
                UIMessageBox.ShowError("请输入问题描述！");
                txtProblemDescription.Focus();
                return false;
            }

            return true;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                // 创建故障信息对象
                FaultInfo = new FaultInfo
                {
                    RecordTime = dtpRecordTime.Value,
                    SNCode = txtSNCode.Text.Trim(),
                    Factor = cmbFactor.Text,
                    Category1 = cmbCategory1.Text,
                    Category2 = cmbCategory2.Text,
                    ProblemDescription = txtProblemDescription.Text.Trim()
                };

                // 保存到数据库
                var db = DBHelper.GetPCDBContext();
                db.Insertable(FaultInfo).ExecuteCommand();

                UIMessageBox.ShowSuccess("故障信息添加成功！");
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"添加故障信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion
    }
}
