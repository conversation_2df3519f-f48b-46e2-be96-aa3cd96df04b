using Fpi.Util;
using Sunny.UI;
using System;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;

namespace LibBusinessModules.UI.DataQuery
{
    /// <summary>
    /// 通用基础数据查询界面
    /// 定义为抽象类，子界面继承此界面去做具体查询实现
    /// </summary>
    public abstract partial class UC_DataQueryBase : UserControl
    {
        #region 属性、字段

        /// <summary>
        /// 数据名称
        /// </summary>
        protected string QueryDataName;

        /// <summary>
        /// 当前页数
        /// </summary>
        protected int CurPage = 1;

        /// <summary>
        /// 总共页数
        /// </summary>
        private int PageCount;

        /// <summary>
        /// 每页记录数
        /// </summary>
        protected int PageSize;

        /// <summary>
        ///SN码
        /// </summary>
        protected string SnCode;

        /// <summary>
        /// 查询起始时间
        /// </summary>
        protected DateTime StartTime;

        /// <summary>
        /// 查询结束时间
        /// </summary>
        protected DateTime EndTime;

        /// <summary>
        /// 本次查到的总数据量
        /// </summary>
        protected int RecordCount;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        #endregion

        #region 构造

        public UC_DataQueryBase()
        {
            InitializeComponent();
        }

        #endregion

        #region 抽象方法（基于此界面去定制的数据查询界面，都要重写以下方法）

        /// <summary>
        /// 设置表头
        /// </summary>
        protected abstract void SetViewHead();

        /// <summary>
        /// 查询总数据量
        /// </summary>
        /// <returns></returns>
        protected abstract int QueryDataCount();

        /// <summary>
        /// 填充数据到控件
        /// </summary>
        protected abstract void FillDataToDgv();

        #endregion

        #region 事件

        private void QueryDataUC_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                InitialDateTimePicker();
                SetViewHead();

                // 禁用点击列表头排序
                foreach(DataGridViewColumn column in dgvRecords.Columns)
                {
                    column.SortMode = DataGridViewColumnSortMode.NotSortable;
                }
            }
        }

        private void btnStartQuery_Click(object sender, EventArgs e)
        {
            dgvRecords.Rows.Clear();
            CurPage = 1;
            lblPage.Text = @"?/?";

            StartTime = dtpStartTime.Value;
            EndTime = dtpEndTime.Value;
            try
            {
                if(!uint.TryParse(txtRecordCount.Text, out uint _))
                {
                    throw new FormatException("每页记录数应为整数，请重新输入！");
                }

                if(StartTime >= EndTime)
                {
                    throw new Exception("起始时间不能大于结束时间！");
                }

                SnCode = txtSnCode.Text;
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError(ex.Message);
                return;
            }

            Enabled = false;

            try
            {
                PageSize = int.Parse(txtRecordCount.Text);

                // 查询数据数量
                try
                {
                    RecordCount = QueryDataCount();
                }
                catch(Exception ex)
                {
                    throw new Exception($"数据库连接异常:{ex.Message}");
                }

                if(RecordCount > 0)
                {
                    PageCount = (RecordCount - 1) / PageSize + 1;
                    QueryResult();
                }
                else
                {
                    PageCount = 0;
                    throw new Exception($"没有此时间段的{QueryDataName}！");
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }

            Enabled = true;
            dgvRecords.Focus();
        }

        /// <summary>
        /// 数据导出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExcelExport_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;
                if(dgvRecords.Rows.Count <= 0)
                {
                    throw new Exception("无数据可导出！");
                }
                else
                {
                    // 导出时弹窗提示选取导出目录，及文件名称
                    string filePath = Application.StartupPath + "\\query\\";
                    if(!Directory.Exists(filePath))
                    {
                        Directory.CreateDirectory(filePath);
                    }
                    // 第一次调用设置初始文件
                    if(string.IsNullOrEmpty(saveFileDialog.FileName))
                    {
                        saveFileDialog.InitialDirectory = filePath;
                    }
                    saveFileDialog.FileName = $"{filePath}{QueryDataName}{dtpStartTime.Value.ToString("yyyy-MM-dd HH：mm：ss")}--" +
                        $"{dtpEndTime.Value.ToString("yyyy-MM-dd HH：mm：ss")}.xlsx";

                    if(saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        // 显示等待界面
                        UIFormServiceHelper.ShowWaitForm(ParentForm, "数据导出中，请稍候...");

                        // 导出
                        FileExportHelper.SaveDataGridViewToExcelFile(ParentForm, dgvRecords, saveFileDialog.FileName);

                        // 线程稍微停一下，否则下面执行很快时等待界面关闭不了。推测是windows消息通信机制问题。
                        //Thread.Sleep(100);
                        // 隐藏等待界面
                        UIFormServiceHelper.HideWaitForm(ParentForm);

                        if(UIMessageBox.ShowAsk("导出成功！是否定位到文件所在位置？"))
                        {
                            var psi = new ProcessStartInfo("Explorer.exe")
                            {
                                Arguments = "/e,/select," + saveFileDialog.FileName
                            };
                            // 打开导出文件所在位置
                            Process.Start(psi);
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                // 线程切换，防止最终进度界面无法关闭
                //Thread.Sleep(100);
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
                UIMessageBox.ShowError(ex.Message);
            }
            finally
            {
                Enabled = true;
                // 线程切换，防止最终进度界面无法关闭
                //Thread.Sleep(100);
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
            }
        }

        #region 翻页

        private void btnFirst_Click(object sender, EventArgs e)
        {
            CurPage = 1;
            try
            {
                QueryResult();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            if(CurPage > 1)
            {
                CurPage--;
                try
                {
                    QueryResult();
                }
                catch(Exception ex)
                {
                    UIMessageBox.ShowError("查询数据错误:" + ex.Message);
                }
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if(CurPage < PageCount)
            {
                CurPage++;
                try
                {
                    QueryResult();
                }
                catch(Exception ex)
                {
                    UIMessageBox.ShowError("查询数据错误:" + ex.Message);
                }
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            if(PageCount <= 0)
                return;
            CurPage = PageCount;
            try
            {
                QueryResult();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }
        }

        #endregion

        #endregion

        #region 方法

        /// <summary>
        /// 得到查询结果并更新界面
        /// </summary>
        private void QueryResult()
        {
            if(PageCount <= 0)
            {
                return;
            }

            lblPage.Text = $"{CurPage}/{PageCount}";

            FillDataToDgv();

            ManageEnable();
        }

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            dtpStartTime.Value = DateTime.Today;
            dtpEndTime.Value = DateTime.Today + new TimeSpan(23, 59, 59);
        }

        /// <summary>
        /// 控制页数按钮的Enable属性
        /// </summary>
        protected void ManageEnable()
        {
            if(CurPage == 1)
            {
                btnFirst.Enabled = btnPrev.Enabled = false;
            }
            else
            {
                btnFirst.Enabled = btnPrev.Enabled = true;
            }

            if(CurPage == PageCount)
            {
                btnEnd.Enabled = btnNext.Enabled = false;
            }
            else
            {
                btnEnd.Enabled = btnNext.Enabled = true;
            }
        }

        #endregion
    }
}